{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "jet-hr-navigator", "title": "Jet HR Navigator", "description": "Helps navigation into Jet HR apps", "icon": "extension-icon.png", "author": "mirkoferrar<PERSON>", "platforms": ["macOS", "Windows"], "license": "MIT", "commands": [{"name": "jet-hr", "title": "Jet HR", "description": "", "mode": "view"}], "dependencies": {"@raycast/api": "^1.103.2", "@raycast/utils": "^1.17.0"}, "devDependencies": {"@raycast/eslint-config": "^2.0.4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "22.13.10", "@types/react": "19.0.10", "eslint": "^9.22.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "prettier": "^3.5.3", "ts-jest": "^29.4.4", "typescript": "^5.8.2"}, "scripts": {"build": "ray build", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "prepublishOnly": "echo \"\\n\\nIt seems like you are trying to publish the Raycast extension to npm.\\n\\nIf you did intend to publish it to npm, remove the \\`prepublishOnly\\` script and rerun \\`npm publish\\` again.\\nIf you wanted to publish it to the Raycast Store instead, use \\`npm run publish\\` instead.\\n\\n\" && exit 1", "publish": "npx @raycast/api@latest publish", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}}