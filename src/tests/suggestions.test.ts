import getSuggestions from '../_suggestions';

describe('getSuggestions', () => {
  describe('SSO Environment Tests', () => {
    it('test_sso_local - should return local SSO URL for local environment', () => {
      const result = getSuggestions('sso local');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'http://sso.jethr.localhost/',
        link: 'http://sso.jethr.localhost/',
        icon: 'globe'
      });
    });

    it('test_sso_staging - should return staging SSO URL for staging environment', () => {
      const result = getSuggestions('sso staging1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://sso.1.jethr.dev/',
        link: 'https://sso.1.jethr.dev/',
        icon: 'globe'
      });
    });

    it('test_sso_prod - should return production SSO URL for production environment', () => {
      const result = getSuggestions('sso prod');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://sso.jethr.com/',
        link: 'https://sso.jethr.com/',
        icon: 'globe'
      });
    });

    it('should handle sso without environment (defaults to local)', () => {
      const result = getSuggestions('sso');

      expect(result).toHaveLength(1);
      expect(result[0].link).toBe('http://sso.jethr.localhost/');
      expect(result[0].title).toBe('Go to url');
    });
  });

  describe('Admin Environment Tests', () => {
    it('test_admin_local - should return local admin URL for local environment', () => {
      const result = getSuggestions('admin local');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'http://admin.jethr.localhost/',
        link: 'http://admin.jethr.localhost/',
        icon: 'globe'
      });
    });

    it('test_admin_staging - should return staging admin URL for staging environment', () => {
      const result = getSuggestions('admin staging1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://admin.1.jethr.dev/',
        link: 'https://admin.1.jethr.dev/',
        icon: 'globe'
      });
    });

    it('test_admin_prod - should return production admin URL for production environment', () => {
      const result = getSuggestions('admin prod');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://admin.jethr.com/',
        link: 'https://admin.jethr.com/',
        icon: 'globe'
      });
    });

    it('should handle admin without environment (defaults to local)', () => {
      const result = getSuggestions('admin');

      expect(result).toHaveLength(1);
      expect(result[0].link).toBe('http://admin.jethr.localhost/');
      expect(result[0].title).toBe('Go to url');
    });
  });

  describe('Backoffice Environment Tests', () => {
    it('test_backoffice_local - should return local backoffice URL', () => {
      const result = getSuggestions('backoffice local');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'http://backoffice.jethr.localhost/',
        link: 'http://backoffice.jethr.localhost/',
        icon: 'globe'
      });
    });

    it('test_backoffice_staging - should return staging backoffice URL', () => {
      const result = getSuggestions('backoffice staging1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://backoffice.1.jethr.dev/',
        link: 'https://backoffice.1.jethr.dev/',
        icon: 'globe'
      });
    });

    it('test_backoffice_prod - should return production backoffice URL', () => {
      const result = getSuggestions('backoffice prod');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://backoffice.jethr.com/',
        link: 'https://backoffice.jethr.com/',
        icon: 'globe'
      });
    });
  });

  describe('Company Environment Tests', () => {
    it('test_company_local - should return local company URL', () => {
      const result = getSuggestions('company local');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'http://backoffice.jethr.localhost/#/aziende/',
        link: 'http://backoffice.jethr.localhost/#/aziende/',
        icon: 'globe'
      });
    });

    it('test_company_staging - should return staging company URL', () => {
      const result = getSuggestions('company staging1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://backoffice.1.jethr.dev/#/aziende/',
        link: 'https://backoffice.1.jethr.dev/#/aziende/',
        icon: 'globe'
      });
    });

    it('test_company_prod - should return production company URL', () => {
      const result = getSuggestions('company prod');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://backoffice.jethr.com/#/aziende/',
        link: 'https://backoffice.jethr.com/#/aziende/',
        icon: 'globe'
      });
    });

    it('test_company_pk_local - should return local company URL with pk', () => {
      const result = getSuggestions('company 1 local');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'http://backoffice.jethr.localhost/#/aziende/1',
        link: 'http://backoffice.jethr.localhost/#/aziende/1',
        icon: 'globe'
      });
    });

    it('test_company_pk_staging - should return staging company URL with pk', () => {
      const result = getSuggestions('company 1 staging1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://backoffice.1.jethr.dev/#/aziende/1',
        link: 'https://backoffice.1.jethr.dev/#/aziende/1',
        icon: 'globe'
      });
    });

    it('test_company_pk_prod - should return production company URL with pk', () => {
      const result = getSuggestions('company 1 prod');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://backoffice.jethr.com/#/aziende/1',
        link: 'https://backoffice.jethr.com/#/aziende/1',
        icon: 'globe'
      });
    });
  });

  describe('Django Environment Tests', () => {
    it('test_django_local - should return local django URL', () => {
      const result = getSuggestions('django local');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'http://backend.jethr.localhost/admin/',
        link: 'http://backend.jethr.localhost/admin/',
        icon: 'globe'
      });
    });

    it('test_django_staging - should return staging django URL', () => {
      const result = getSuggestions('django staging1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://backend.1.jethr.dev/admin/',
        link: 'https://backend.1.jethr.dev/admin/',
        icon: 'globe'
      });
    });

    it('test_django_prod - should return production django URL', () => {
      const result = getSuggestions('django prod');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://backend.jethr.com/admin/',
        link: 'https://backend.jethr.com/admin/',
        icon: 'globe'
      });
    });

    it('test_django_model_pk_local - should return local django URL with model and pk', () => {
      const result = getSuggestions('django local employee 1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'http://backend.jethr.localhost/admin/jet/employee/1/',
        link: 'http://backend.jethr.localhost/admin/jet/employee/1/',
        icon: 'globe'
      });
    });

    it('test_django_model_pk_staging - should return staging django URL with model and pk', () => {
      const result = getSuggestions('django staging1 employee 1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://backend.1.jethr.dev/admin/jet/employee/1/',
        link: 'https://backend.1.jethr.dev/admin/jet/employee/1/',
        icon: 'globe'
      });
    });

    it('test_django_model_pk_prod - should return production django URL with model and pk', () => {
      const result = getSuggestions('django prod employee 1');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        title: 'Go to url',
        subtitle: 'https://backend.jethr.com/admin/jet/employee/1/',
        link: 'https://backend.jethr.com/admin/jet/employee/1/',
        icon: 'globe'
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should return default SSO local for empty input', () => {
      const result = getSuggestions('');

      expect(result).toHaveLength(1);
      expect(result[0].link).toBe('http://sso.jethr.localhost/');
    });

    it('should return default SSO local for whitespace only input', () => {
      const result = getSuggestions('   ');

      expect(result).toHaveLength(1);
      expect(result[0].link).toBe('http://sso.jethr.localhost/');
    });

    it('should handle case insensitive input', () => {
      const result1 = getSuggestions('SSO LOCAL');
      const result2 = getSuggestions('sso local');
      const result3 = getSuggestions('Sso Local');

      expect(result1).toEqual(result2);
      expect(result2).toEqual(result3);
    });

    it('should return default SSO local for unknown keywords', () => {
      const result = getSuggestions('unknown keyword');

      expect(result).toHaveLength(1);
      expect(result[0].link).toBe('http://sso.jethr.localhost/');
    });

    it('should handle partial matches', () => {
      const result = getSuggestions('ss');

      // Should return default SSO local
      expect(result.length).toBe(1);
      expect(result[0].link).toBe('http://sso.jethr.localhost/');
    });

    it('should handle multiple spaces between keywords', () => {
      const result = getSuggestions('sso    local');

      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Go to url');
      expect(result[0].link).toBe('http://sso.jethr.localhost/');
    });
  });

  describe('Multiple Environment Suggestions', () => {
    it('should return default local environment when only target is specified', () => {
      const result = getSuggestions('sso');

      expect(result.length).toBe(1); // defaults to local
      expect(result[0].link).toBe('http://sso.jethr.localhost/');
    });

    it('should return default local environment for admin', () => {
      const result = getSuggestions('admin');

      expect(result.length).toBe(1);
      expect(result[0].link).toBe('http://admin.jethr.localhost/');
      expect(result[0].title).toBe('Go to url');
    });
  });

  describe('URL Structure Validation', () => {
    it('should generate correct local URLs', () => {
      const targets = ['sso', 'admin', 'backoffice', 'company'];

      targets.forEach(target => {
        const result = getSuggestions(`${target} local`);
        expect(result[0].link).toMatch(/^http:\/\/.*\.jethr\.localhost/);
      });
    });

    it('should generate correct staging URLs', () => {
      const targets = ['sso', 'admin', 'backoffice', 'company'];

      targets.forEach(target => {
        const result = getSuggestions(`${target} staging1`);
        expect(result[0].link).toMatch(/^https:\/\/.*\.1\.jethr\.dev/);
      });
    });

    it('should generate correct production URLs', () => {
      const targets = ['sso', 'admin', 'backoffice', 'company'];

      targets.forEach(target => {
        const result = getSuggestions(`${target} prod`);
        expect(result[0].link).toMatch(/^https:\/\/.*\.jethr\.com/);
      });
    });

    it('should handle django URLs with different subdomain structure', () => {
      const localResult = getSuggestions('django local');
      const stagingResult = getSuggestions('django staging1');
      const prodResult = getSuggestions('django prod');

      expect(localResult[0].link).toBe('http://backend.jethr.localhost/admin/');
      expect(stagingResult[0].link).toBe('https://backend.1.jethr.dev/admin/');
      expect(prodResult[0].link).toBe('https://backend.jethr.com/admin/');
    });
  });
});
