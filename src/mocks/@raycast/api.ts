// Mock for @raycast/api
export const Icon = {
  Globe: 'globe',
  Person: 'person',
  Building: 'building',
  Gear: 'gear',
  List: 'list',
  Document: 'document',
  Folder: 'folder',
  Star: 'star',
  Heart: 'heart',
  Eye: 'eye',
  Lock: 'lock',
  Key: 'key',
  Shield: 'shield',
  Warning: 'warning',
  ExclamationMark: 'exclamation-mark',
  QuestionMark: 'question-mark',
  Info: 'info',
  Plus: 'plus',
  Minus: 'minus',
  Multiply: 'multiply',
  Divide: 'divide',
  Equal: 'equal',
  ArrowUp: 'arrow-up',
  ArrowDown: 'arrow-down',
  ArrowLeft: 'arrow-left',
  ArrowRight: 'arrow-right',
  ChevronUp: 'chevron-up',
  ChevronDown: 'chevron-down',
  ChevronLeft: 'chevron-left',
  ChevronRight: 'chevron-right',
  Check: 'check',
  Cross: 'cross',
  Circle: 'circle',
  Square: 'square',
  Triangle: 'triangle',
  Diamond: 'diamond',
  Hashtag: 'hashtag',
  AtSymbol: 'at-symbol',
  Percent: 'percent',
  Dollar: 'dollar',
  Euro: 'euro',
  Pound: 'pound',
  Yen: 'yen',
  Calendar: 'calendar',
  Clock: 'clock',
  Stopwatch: 'stopwatch',
  Timer: 'timer',
  Bell: 'bell',
  BellDisabled: 'bell-disabled',
  Volume: 'volume',
  VolumeDisabled: 'volume-disabled',
  Microphone: 'microphone',
  MicrophoneDisabled: 'microphone-disabled',
  Camera: 'camera',
  CameraDisabled: 'camera-disabled',
  Video: 'video',
  VideoDisabled: 'video-disabled',
  Image: 'image',
  Music: 'music',
  Headphones: 'headphones',
  Speaker: 'speaker',
  Phone: 'phone',
  Mobile: 'mobile',
  Laptop: 'laptop',
  Desktop: 'desktop',
  Monitor: 'monitor',
  Keyboard: 'keyboard',
  Mouse: 'mouse',
  Printer: 'printer',
  Scanner: 'scanner',
  HardDrive: 'hard-drive',
  MemoryStick: 'memory-stick',
  Cd: 'cd',
  Dvd: 'dvd',
  Floppy: 'floppy',
  Trash: 'trash',
  TrashFull: 'trash-full',
  Download: 'download',
  Upload: 'upload',
  Cloud: 'cloud',
  CloudDownload: 'cloud-download',
  CloudUpload: 'cloud-upload',
  Wifi: 'wifi',
  WifiDisabled: 'wifi-disabled',
  Bluetooth: 'bluetooth',
  BluetoothDisabled: 'bluetooth-disabled',
  Battery: 'battery',
  BatteryCharging: 'battery-charging',
  BatteryDisabled: 'battery-disabled',
  Power: 'power',
  PowerDisabled: 'power-disabled',
  Plug: 'plug',
  PlugDisabled: 'plug-disabled',
  Lightbulb: 'lightbulb',
  LightbulbDisabled: 'lightbulb-disabled',
  Sun: 'sun',
  Moon: 'moon',
  Stars: 'stars',
  Weather: 'weather',
  Umbrella: 'umbrella',
  Thermometer: 'thermometer',
  Fire: 'fire',
  Snowflake: 'snowflake',
  Drop: 'drop',
  Wind: 'wind',
  Tornado: 'tornado',
  Lightning: 'lightning',
  Earthquake: 'earthquake',
  Volcano: 'volcano',
  Mountain: 'mountain',
  Tree: 'tree',
  Leaf: 'leaf',
  Flower: 'flower',
  Seedling: 'seedling',
  Cactus: 'cactus',
  Herb: 'herb',
  Shamrock: 'shamrock',
  PineTree: 'pine-tree',
  PalmTree: 'palm-tree',
  DeciduousTree: 'deciduous-tree',
  EvergreenTree: 'evergreen-tree',
  Tulip: 'tulip',
  Rose: 'rose',
  Sunflower: 'sunflower',
  Blossom: 'blossom',
  CherryBlossom: 'cherry-blossom',
  Hibiscus: 'hibiscus',
  Bouquet: 'bouquet',
  WiltedFlower: 'wilted-flower',
  Mushroom: 'mushroom',
  ChestnutTree: 'chestnut-tree',
  MapleLeaf: 'maple-leaf',
  FallenLeaves: 'fallen-leaves',
  Leaves: 'leaves',
  EmptyLeaves: 'empty-leaves',
  Grapes: 'grapes',
  Melon: 'melon',
  Watermelon: 'watermelon',
  Tangerine: 'tangerine',
  Lemon: 'lemon',
  Banana: 'banana',
  Pineapple: 'pineapple',
  Apple: 'apple',
  GreenApple: 'green-apple',
  Pear: 'pear',
  Peach: 'peach',
  Cherries: 'cherries',
  Strawberry: 'strawberry',
  Kiwi: 'kiwi',
  Tomato: 'tomato',
  Coconut: 'coconut',
  Avocado: 'avocado',
  Eggplant: 'eggplant',
  Potato: 'potato',
  Carrot: 'carrot',
  Corn: 'corn',
  HotPepper: 'hot-pepper',
  Cucumber: 'cucumber',
  Broccoli: 'broccoli',
  Garlic: 'garlic',
  Onion: 'onion',
  Peanuts: 'peanuts',
  Chestnut: 'chestnut',
  Bread: 'bread',
  Croissant: 'croissant',
  Baguette: 'baguette',
  Pretzel: 'pretzel',
  Bagel: 'bagel',
  Pancakes: 'pancakes',
  Cheese: 'cheese',
  MeatOnBone: 'meat-on-bone',
  PoultryLeg: 'poultry-leg',
  CutOfMeat: 'cut-of-meat',
  Bacon: 'bacon',
  Hamburger: 'hamburger',
  FrenchFries: 'french-fries',
  Pizza: 'pizza',
  HotDog: 'hot-dog',
  Sandwich: 'sandwich',
  Taco: 'taco',
  Burrito: 'burrito',
  Salad: 'salad',
  ShallowPanOfFood: 'shallow-pan-of-food',
  CannedFood: 'canned-food',
  Spaghetti: 'spaghetti',
  Ramen: 'ramen',
  Stew: 'stew',
  Fish: 'fish',
  FriedShrimp: 'fried-shrimp',
  Sushi: 'sushi',
  Bento: 'bento',
  Curry: 'curry',
  Rice: 'rice',
  RiceBall: 'rice-ball',
  RiceCracker: 'rice-cracker',
  Oden: 'oden',
  Dango: 'dango',
  Shaved: 'shaved',
  IceCream: 'ice-cream',
  Icecream: 'icecream',
  Pie: 'pie',
  Cake: 'cake',
  Cupcake: 'cupcake',
  Cookie: 'cookie',
  Chocolate: 'chocolate',
  Candy: 'candy',
  Lollipop: 'lollipop',
  Custard: 'custard',
  Honey: 'honey',
  Milk: 'milk',
  Baby: 'baby',
  HotBeverage: 'hot-beverage',
  Teacup: 'teacup',
  Sake: 'sake',
  Wine: 'wine',
  Cocktail: 'cocktail',
  TropicalDrink: 'tropical-drink',
  Beer: 'beer',
  Beers: 'beers',
  Clinking: 'clinking',
  TumblerGlass: 'tumbler-glass',
  Cup: 'cup',
  Teapot: 'teapot',
  Amphora: 'amphora',
  Grapes2: 'grapes2',
  Melon2: 'melon2',
  Watermelon2: 'watermelon2',
  Tangerine2: 'tangerine2',
  Lemon2: 'lemon2',
  Banana2: 'banana2',
  Pineapple2: 'pineapple2',
  Apple2: 'apple2',
  GreenApple2: 'green-apple2',
  Pear2: 'pear2',
  Peach2: 'peach2',
  Cherries2: 'cherries2',
  Strawberry2: 'strawberry2',
  Kiwi2: 'kiwi2',
  Tomato2: 'tomato2',
  Coconut2: 'coconut2',
  Avocado2: 'avocado2',
  Eggplant2: 'eggplant2',
  Potato2: 'potato2',
  Carrot2: 'carrot2',
  Corn2: 'corn2',
  HotPepper2: 'hot-pepper2',
  Cucumber2: 'cucumber2',
  Broccoli2: 'broccoli2',
  Garlic2: 'garlic2',
  Onion2: 'onion2',
  Peanuts2: 'peanuts2',
  Chestnut2: 'chestnut2'
};

export const Action = {
  OpenInBrowser: jest.fn(),
  CopyToClipboard: jest.fn(),
  Paste: jest.fn(),
  ShowInFinder: jest.fn(),
  OpenWith: jest.fn(),
  ToggleQuickLook: jest.fn(),
  Push: jest.fn(),
  Pop: jest.fn(),
  CreateQuicklink: jest.fn(),
  SubmitForm: jest.fn(),
  Open: jest.fn()
};

export const ActionPanel = jest.fn(({ children }) => children);
export const List = jest.fn(({ children }) => children);
export const ListItem = jest.fn();
export const Detail = jest.fn();
export const Form = jest.fn();
export const Toast = jest.fn();
export const showToast = jest.fn();
export const getPreferenceValues = jest.fn(() => ({}));
export const showHUD = jest.fn();
export const popToRoot = jest.fn();
export const closeMainWindow = jest.fn();
export const launchCommand = jest.fn();
export const openCommandPreferences = jest.fn();
export const openExtensionPreferences = jest.fn();
export const Clipboard = {
  read: jest.fn(),
  readText: jest.fn(),
  copy: jest.fn(),
  paste: jest.fn(),
  clear: jest.fn()
};

export const LocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  allItems: jest.fn()
};

export const Cache = {
  get: jest.fn(),
  set: jest.fn(),
  remove: jest.fn(),
  clear: jest.fn()
};

export const environment = {
  raycastVersion: '1.0.0',
  extensionName: 'test-extension',
  commandName: 'test-command',
  commandMode: 'view',
  isDevelopment: true,
  supportPath: '/tmp',
  assetsPath: '/tmp/assets',
  textSize: 'medium',
  appearance: 'light'
};

export default {
  Icon,
  Action,
  ActionPanel,
  List,
  ListItem,
  Detail,
  Form,
  Toast,
  showToast,
  getPreferenceValues,
  showHUD,
  popToRoot,
  closeMainWindow,
  launchCommand,
  openCommandPreferences,
  openExtensionPreferences,
  Clipboard,
  LocalStorage,
  Cache,
  environment
};