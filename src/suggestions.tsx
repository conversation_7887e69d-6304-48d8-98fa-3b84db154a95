import { Icon } from "@raycast/api";

const ACTIONS = {
  GOTOURL: "GOTOURL",
};

const ACTION_KEYWORDS = {
  [ACTIONS.GOTOURL]: [],
};
const ENV = {
  LOCAL: "LOCAL",
  PROD: "PROD",
  STAGING: "STAGING",
};
const ENV_KEYWORDS = {
  [ENV.LOCAL]: ["loc", "local"],
  [ENV.PROD]: ["prod", "production"],
  [ENV.STAGING]: ["staging"],
};

const APP = {
  SSO: "SSO",
  ADMIN: "ADMIN",
  BACKOFFICE: "BACKOFFICE",
  DJANGO: "DJANGO",
};
const APP_KEYWORDS = {
  [APP.SSO]: ["sso"],
  [APP.ADMIN]: ["admin"],
  [APP.BACKOFFICE]: ["bo", "backoffice"],
  [APP.DJANGO]: ["dj", "django"],
};

const MODEL = {
  COMPANY: "COMPANY",
  EMPLOYEE: "EMPLOYEE",
};
const MODEL_KEYWORDS = {
  [MODEL.COMPANY]: ["company"],
  [MODEL.EMPLOYEE]: ["employee"],
};

function matchKeyword(node: string, keyword: string | RegExp): boolean {
  return Boolean((keyword instanceof RegExp && node.match(keyword)) || node === keyword);
}

function popKeywords(nodes: string[], keywords: object, defaultKey: string): string[] {
  for (const nodeIndex in nodes) {
    const node = nodes[nodeIndex];
    for (const entry of Object.entries(keywords)) {
      const [keywordKey, keywordMatches] = entry;
      for (const keywordMatch of keywordMatches) {
        if (matchKeyword(node, keywordMatch)) {
          nodes.splice(parseInt(nodeIndex, 10), 1);
          return [keywordKey, node];
        }
      }
    }
  }
  const defaultValue = keywords[defaultKey][0];
  return [defaultKey, defaultValue];
}

function getUrlSubdomain(target: string) {
  return (
    {
      [TARGET.SSO]: "sso",
      [TARGET.DJANGO]: "backend",
      [TARGET.ADMIN]: "admin",
      [TARGET.BACKOFFICE]: "backoffice",
      [TARGET.COMPANY]: "backoffice",
    }[target] || target
  );
}

function getUrlSubdirectory(target: string, nodes: string[]) {
  if (target === TARGET.DJANGO) {
    if (nodes.length === 0) {
      return "admin/";
    }
    if (nodes.length === 2 && /^\d+$/.test(nodes[1])) {
      return `admin/jet/${nodes[0]}/${nodes[1]}/`;
    }
    return `admin/jet/${nodes[0]}/?q=${nodes.slice(1).join(" ")}`;
  }
  if (target === TARGET.COMPANY) {
    return `#/aziende/${nodes.join("")}`;
  }
  return "";
}

function buildUrlSuggestion(title: string, url: string, priority: number = 0) {
  return {
    icon: Icon.Globe,
    title: title,
    subtitle: url,
    link: url,
    priority,
  };
}

function buildDjangoUrlSuggestions(keywords: string[], numbers: int[], models: string[], extraWords: string[]) {
  const suggestions = [];

  const LOCAL_PRIO = keywords.includes("local") ? 5 : 1;
  const STAGING_PRIO = numbers.length > 0 || keywords.includes("staging") ? 10 : 0;
  const PROD_PRIO = keywords.includes("prod") ? 20 : 2;
  const MODEL_PRIO = 1;
  const MODEL_PK_PRIO = 2;
  const MODEL_SEARCH_PRIO = 3;

  numbers.forEach((number) => {
    suggestions.push(
      buildUrlSuggestion(`Django Staging ${number}`, `https://backend.${number}.jethr.dev/admin/`, STAGING_PRIO),
    );
  });
  suggestions.push(buildUrlSuggestion("Django Local", "http://backend.jethr.localhost/admin/", LOCAL_PRIO));
  suggestions.push(buildUrlSuggestion("Django Prod", "https://backend.jethr.com/admin/", PROD_PRIO));
  models.forEach((model) => {
    suggestions.push(
      buildUrlSuggestion(
        `Django Prod Model ${model}`,
        `https://backend.jethr.com/admin/jet/${model}/`,
        MODEL_PRIO + PROD_PRIO,
      ),
    );
    numbers.forEach((pk) => {
      suggestions.push(
        buildUrlSuggestion(
          `Django Prod Model ${model} ${pk}`,
          `https://backend.jethr.com/admin/jet/${model}/${pk}/`,
          MODEL_PK_PRIO + PROD_PRIO,
        ),
      );
    });
    if (extraWords.length > 0) {
      suggestions.push(
        buildUrlSuggestion(
          `Django Prod Model ${model} search ${extraWords.join(" ")}`,
          `https://backend.jethr.com/admin/jet/${model}/?q=${extraWords.join(" ")}`,
          MODEL_SEARCH_PRIO + PROD_PRIO,
        ),
      );
    }
  });
  return suggestions;
}

function extractNumbersFromNodes(nodes: string[]): int[] {
  return nodes
    .filter((node) => /^\d+$/.test(node))
    .map((node) => parseInt(node, 10))
    .sort();
}

function extractCodenamesAndKeywordsFromNodes(nodes: string[], keywords_by_codename: object): [string[], string[]] {
  const codenames: string[] = [];
  const matchedKeywords: string[] = [];
  Object.entries(keywords_by_codename).forEach(([codename, keywords]) => {
    keywords.forEach((keyword) => {
      nodes.forEach((node) => {
        if (node == keyword) {
          codenames.push(codename);
          matchedKeywords.push(keyword);
        }
      });
    });
  });
  return [codenames, matchedKeywords];
}

class Context {
  nodes: string[];
  numbers: int[];
  envs: string[];
  apps: string[];
  models: string[];
  extraWords: string[];

  
}

function extractContextFromSearchText(searchText: string) {
  const nodes = searchText.split(" ").filter((node) => node);
  const numbers = extractNumbersFromNodes(nodes);
  const [envCodenames, envKeywords] = extractCodenamesAndKeywordsFromNodes(nodes, ENV_KEYWORDS);
  const [appCodenames, appKeywords] = extractCodenamesAndKeywordsFromNodes(nodes, APP_KEYWORDS);
  const [modelCodenames, modelKeywords] = extractCodenamesAndKeywordsFromNodes(nodes, MODEL_KEYWORDS);
  const allKeywords = [...numbers.map(String), ...envKeywords, ...appKeywords, ...modelKeywords];
  const extraWords = nodes.filter((node) => !allKeywords.includes(node));

  return new Context(nodes, numbers, envCodenames, appCodenames, modelCodenames, extraWords);
}

function buildSuggestions(context: Context) {
  const suggestions = [];
  suggestions.push(...buildDjangoUrlSuggestions(context));
  suggestions.sort((a, b) => b.priority - a.priority);
  return suggestions;
}

export default (searchText: string) => {
  const context = extractContextFromSearchText(searchText);
  return buildSuggestions(context);
};
