import { Icon } from "@raycast/api";

const ACTIONS = {
  GOTOURL: "GOTOURL",
};

const ACTION_KEYWORDS = {
  [ACTIONS.GOTOURL]: [],
};
const ENV = {
  LOCAL: "LOCAL",
  PROD: "PROD",
  STAGING: "STAGING",
};
const ENV_KEYWORDS = {
  [ENV.LOCAL]: ["loc", "local"],
  [ENV.PROD]: ["prod", "production"],
  [ENV.STAGING]: [new RegExp("stg\\d+"), new RegExp("staging\\d+")],
};

const TARGET = {
  SSO: "SSO",
  ADMIN: "ADMIN",
  BACKOFFICE: "BACKOFFICE",
  COMPANY: "COMPANY",
  DJANGO: "DJANGO",
};
const TARGET_KEYWORDS = {
  [TARGET.SSO]: ["sso"],
  [TARGET.ADMIN]: ["admin"],
  [TARGET.BACKOFFICE]: ["bo", "backoffice"],
  [TARGET.COMPANY]: ["company"],
  [TARGET.DJANGO]: ["dj", "django"],
};

function matchKeyword(node: string, keyword: string | RegExp): boolean {
  return Boolean((keyword instanceof RegExp && node.match(keyword)) || node === keyword);
}

function popKeywords(nodes: string[], keywords: object, defaultKey: string): string[] {
  for (const nodeIndex in nodes) {
    const node = nodes[nodeIndex];
    for (const entry of Object.entries(keywords)) {
      const [keywordKey, keywordMatches] = entry;
      for (const keywordMatch of keywordMatches) {
        if (matchKeyword(node, keywordMatch)) {
          nodes.splice(parseInt(nodeIndex, 10), 1);
          return [keywordKey, node];
        }
      }
    }
  }
  const defaultValue = keywords[defaultKey][0];
  return [defaultKey, defaultValue];
}

function getUrlSubdomain(target: string) {
  return (
    {
      [TARGET.SSO]: "sso",
      [TARGET.DJANGO]: "backend",
      [TARGET.ADMIN]: "admin",
      [TARGET.BACKOFFICE]: "backoffice",
      [TARGET.COMPANY]: "backoffice",
    }[target] || target
  );
}

function getUrlSubdirectory(target: string, nodes: string[]) {
  if (target === TARGET.DJANGO) {
    if (nodes.length === 0) {
      return "admin/";
    }
    if (nodes.length === 2 && /^\d+$/.test(nodes[1])) {
      return `admin/jet/${nodes[0]}/${nodes[1]}/`;
    }
    return `admin/jet/${nodes[0]}/?q=${nodes.slice(1).join(" ")}`;
  }
  if (target === TARGET.COMPANY) {
    return `#/aziende/${nodes.join("")}`;
  }
  return "";
}

export default (searchText: string) => {
  const nodes = searchText.split(" ").filter((node) => node);
  const action = popKeywords(nodes, ACTION_KEYWORDS, ACTIONS.GOTOURL)[0];

  if (action === ACTIONS.GOTOURL) {
    const [envKW, envNode] = popKeywords(nodes, ENV_KEYWORDS, ENV.LOCAL);
    if (!envKW) {
      return [];
    }

    const target = popKeywords(nodes, TARGET_KEYWORDS, TARGET.SSO)[0];
    const urlSubdomain = getUrlSubdomain(target);
    const urlSubdirectory = getUrlSubdirectory(target, nodes);
    const url = {
      [ENV.LOCAL]: `http://${urlSubdomain}.jethr.localhost/${urlSubdirectory}`,
      [ENV.PROD]: `https://${urlSubdomain}.jethr.com/${urlSubdirectory}`,
      [ENV.STAGING]: `https://${urlSubdomain}.${envNode.slice(7)}.jethr.dev/${urlSubdirectory}`,
    }[envKW];

    return [
      {
        icon: Icon.Globe,
        title: "Go to url",
        subtitle: url,
        link: url,
      },
    ];
  }

  return [];
};
